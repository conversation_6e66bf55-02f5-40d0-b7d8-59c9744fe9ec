import cron from "node-cron";
import { monitorAllShopsRTP } from "./rtpMonitoringService.js";
import logger from "../lib/logger.js";

/**
 * RTP Monitoring Scheduler
 * Runs periodic RTP checks and generates alerts
 */

let isMonitoringActive = false;
let lastMonitoringRun = null;
let monitoringStats = {
  totalRuns: 0,
  alertsGenerated: 0,
  lastAlerts: [],
};

/**
 * Setup RTP monitoring scheduler
 */
export const setupRTPMonitoringScheduler = () => {
  // Run RTP monitoring every 5 minutes
  cron.schedule("*/5 * * * *", async () => {
    if (isMonitoringActive) {
      logger.warn("RTP monitoring already in progress, skipping this run");
      return;
    }

    try {
      isMonitoringActive = true;
      lastMonitoringRun = new Date();
      monitoringStats.totalRuns++;

      logger.info("Starting scheduled RTP monitoring...");
      const alerts = await monitorAllShopsRTP();

      if (alerts.length > 0) {
        monitoringStats.alertsGenerated += alerts.length;
        monitoringStats.lastAlerts = alerts.slice(-10); // Keep last 10 alerts

        logger.warn(
          `RTP monitoring generated ${alerts.length} alerts:`,
          alerts.map(alert => ({
            shop: alert.shopName,
            status: alert.status,
            currentRtp: `${(alert.currentRtp * 100).toFixed(2)}%`,
            targetRtp: `${(alert.targetRtp * 100).toFixed(2)}%`,
            action: alert.recommendedAction,
          }))
        );

        // Here you could integrate with alerting systems:
        // - Send emails to administrators
        // - Push notifications to monitoring dashboards
        // - Slack/Discord webhooks
        // - Database alerts table
      } else {
        logger.info("RTP monitoring completed - no alerts generated");
      }

    } catch (error) {
      logger.error("RTP monitoring failed:", error);
    } finally {
      isMonitoringActive = false;
    }
  });

  // Run comprehensive RTP analysis every hour
  cron.schedule("0 * * * *", async () => {
    try {
      logger.info("Running hourly RTP comprehensive analysis...");
      
      // This could include:
      // - Trend analysis
      // - Performance metrics
      // - Configuration effectiveness analysis
      // - Predictive modeling for future RTP needs
      
      logger.info("Hourly RTP analysis completed");
    } catch (error) {
      logger.error("Hourly RTP analysis failed:", error);
    }
  });

  logger.info("RTP monitoring scheduler initialized");
};

/**
 * Get monitoring statistics
 */
export const getMonitoringStats = () => {
  return {
    ...monitoringStats,
    isActive: isMonitoringActive,
    lastRun: lastMonitoringRun,
    uptime: lastMonitoringRun ? Date.now() - lastMonitoringRun.getTime() : null,
  };
};

/**
 * Force run RTP monitoring (for testing/manual triggers)
 */
export const forceRTPMonitoring = async () => {
  if (isMonitoringActive) {
    throw new Error("RTP monitoring already in progress");
  }

  try {
    isMonitoringActive = true;
    logger.info("Manual RTP monitoring triggered");
    
    const alerts = await monitorAllShopsRTP();
    
    return {
      success: true,
      alertsGenerated: alerts.length,
      alerts,
      timestamp: new Date(),
    };
  } finally {
    isMonitoringActive = false;
  }
};
